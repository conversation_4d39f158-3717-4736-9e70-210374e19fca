# -*- coding: utf-8 -*-
"""
Created on Tue Feb 11 10:48:39 2025

@author: kevguima
"""

import streamlit as st
from storyboard import generate_storyboard, ImageModel, AspectRatio, Style
import asyncio
import io


DEFAULT_IMAGE_MODEL = ImageModel.OPENAI_DALLE_3
DEFAULT_ASPECT_RATIO = AspectRatio.WIDESCREEN
DEFAULT_STYLE = Style.SKETCHY_BW_GRAPHIC


def set_progress_callback(message):
    """Progress callback for Streamlit"""
    st.session_state.status = message


def generate_storyboard_streamlit(description, expectations, image_model, nb_steps, aspect_ratio, style, style_description, avoided_terms):
    """Generate storyboard with Streamlit progress updates"""
    try:
        expectations_list = expectations.split("\n") if expectations else []
        avoided_terms_list = avoided_terms if avoided_terms else []

        prs = asyncio.run(generate_storyboard(
            set_progress_callback,
            description,
            expectations_list,
            ImageModel[image_model],
            nb_steps,
            AspectRatio[aspect_ratio],
            Style[style],
            style_description,
            avoided_terms_list
        ))

        # Save to bytes buffer
        buffer = io.BytesIO()
        prs.save(buffer)
        buffer.seek(0)
        return buffer.getvalue()

    except Exception as e:
        st.error(f"An error occurred: {e}")
        return None


def main():
    st.set_page_config(page_title="Storyboard Generation", layout="wide")

    # Initialize session state
    if 'status' not in st.session_state:
        st.session_state.status = "Idle"
    if 'avoided_terms' not in st.session_state:
        st.session_state.avoided_terms = []

    st.title("Storyboard Generation")

    # Settings section
    st.header("Settings")

    col1, col2 = st.columns(2)

    with col1:
        # Image model selection
        image_model = st.selectbox(
            "Image model",
            options=[model.name for model in ImageModel],
            format_func=lambda x: ImageModel[x].value,
            index=0
        )

        # Number of steps
        nb_steps = st.number_input(
            "Number of steps",
            min_value=1,
            max_value=30,
            step=1,
            value=None
        )

        # Aspect ratio
        aspect_ratio = st.selectbox(
            "Image aspect ratio",
            options=[ratio.name for ratio in AspectRatio],
            format_func=lambda x: AspectRatio[x].value,
            index=0
        )

        # Style
        style = st.selectbox(
            "Style",
            options=[s.name for s in Style],
            format_func=lambda x: Style[x].value,
            index=0
        )

        # Style description (only show if Custom is selected)
        style_description = ""
        if style == Style.CUSTOM.name:
            style_description = st.text_input("Custom style description")

    with col2:
        # Project description
        description = st.text_area(
            "Project description",
            height=100
        )

        # Insights
        expectations = st.text_area(
            "Insights to leverage in the story",
            height=150
        )

        # Terms to avoid
        st.subheader("Terms to avoid in the prompt")

        # Add term functionality
        col_term1, col_term2 = st.columns([3, 1])
        with col_term1:
            new_term = st.text_input("Add term", key="new_term_input")
        with col_term2:
            if st.button("Add term"):
                if new_term and new_term not in st.session_state.avoided_terms:
                    st.session_state.avoided_terms.append(new_term)
                    st.rerun()

        # Display avoided terms
        if st.session_state.avoided_terms:
            avoided_terms = st.multiselect(
                "Selected terms to avoid",
                options=st.session_state.avoided_terms,
                default=st.session_state.avoided_terms
            )
        else:
            avoided_terms = []

    # Action buttons
    st.divider()

    col_btn1, col_btn2 = st.columns([1, 1])

    with col_btn1:
        generate_clicked = st.button("Generate", type="primary")

    with col_btn2:
        if st.button("Reset"):
            st.session_state.avoided_terms = []
            st.rerun()

    # Status display
    st.info(f"Status: {st.session_state.status}")

    # Generate storyboard
    if generate_clicked:
        if not description:
            st.error("Please provide a project description")
        else:
            with st.spinner("Generating storyboard..."):
                st.session_state.status = "Generating..."

                pptx_data = generate_storyboard_streamlit(
                    description=description,
                    expectations=expectations,
                    image_model=image_model,
                    nb_steps=nb_steps,
                    aspect_ratio=aspect_ratio,
                    style=style,
                    style_description=style_description,
                    avoided_terms=avoided_terms
                )

                if pptx_data:
                    st.session_state.status = "Completed"
                    st.success("Storyboard generated successfully!")

                    # Download button
                    st.download_button(
                        label="Download Storyboard",
                        data=pptx_data,
                        file_name="storyboard.pptx",
                        mime="application/vnd.openxmlformats-officedocument.presentationml.presentation"
                    )
                else:
                    st.session_state.status = "Failed"


if __name__ == '__main__':
    main()

# -*- coding: utf-8 -*-
"""
Created on Tue Feb 11 10:48:39 2025

@author: kevguima
"""

from storyboard import generate_storyboard, ImageModel, AspectRatio, Style
from dash import Dash, html, dcc, callback, Output, Input, State
import dash_bootstrap_components as dbc
from dash.long_callback import DiskcacheLongCallbackManager
import diskcache
import asyncio


cache = diskcache.Cache("cache")
long_callback_manager = DiskcacheLongCallbackManager(cache)


DEFAULT_IMAGE_MODEL = ImageModel.OPENAI_DALLE_3

DEFAULT_ASPECT_RATIO = AspectRatio.WIDESCREEN

DEFAULT_STYLE = Style.SKETCHY_BW_GRAPHIC


@callback(
    output=Output(component_id="generated-file", component_property="data"),
    inputs=[Input(component_id="generate", component_property="n_clicks")],
    state=[State(component_id="description", component_property="value"),
           State(component_id="expectations", component_property="value"),
           State(component_id="image_model", component_property="value"),
           State(component_id="nb_steps", component_property="value"),
           State(component_id="aspect_ratio", component_property="value"),
           State(component_id="style", component_property="value"),
           State(component_id="style_description", component_property="value"),
           State(component_id="avoided_terms", component_property="value")],
    running=[(Output("generate", "disabled"), True, False),
             (Output("cancel", "disabled"), False, True)],
    progress=[Output("status", "children")],
    cancel=[Input("cancel", "n_clicks")],
    prevent_initial_call=True,
    background=True,
    manager=long_callback_manager
)
def generate_callback(set_progress, n_clicks, description, expectations, image_model, nb_steps, aspect_ratio, style, style_description, avoided_terms):
    try:
        prs = asyncio.run(generate_storyboard(set_progress, description, expectations.split("\n"), ImageModel[image_model], nb_steps, AspectRatio[aspect_ratio], Style[style], style_description, avoided_terms))
    except Exception as e:
        set_progress(f"An error occurred: {e}")
        return None

    set_progress("Idle")

    return dcc.send_bytes(lambda buffer: prs.save(buffer), "storyboard.pptx")


@callback(
    output=[Output("avoided_terms", "options"),
            Output("input_term", "value")],
    inputs=[Input("add_term", "n_clicks")],
    state=[State("input_term", "value"),
           State("avoided_terms", "options")],
    prevent_initial_call=True
)
def add_term_callback(n_clicks, new_value, current_options):
    current_options.append({"label": new_value, "value": new_value})

    return [current_options, ""]


@callback(
    output=[Output("image_model", "value"),
            Output("nb_steps", "value"),
            Output("aspect_ratio", "value"),
            Output("style", "value"),
            Output("description", "value"),
            Output("expectations", "value"),
            Output("avoided_terms", "value")],
    inputs=[Input("reset", "n_clicks")],
)
def reset_page(n_clicks):
    return [ImageModel.OPENAI_DALLE_3.name, None, DEFAULT_ASPECT_RATIO.name, DEFAULT_STYLE.name, "", "", ""]


@callback(
    output=Output("style_description", "style"),
    inputs=[Input("style", "value")],
    state=[State("style_description", "style")]
)
def select_style(value, description_style):
    description_style["display"] = "block" if value == Style.CUSTOM.name else "none"
    return description_style


app = Dash(external_stylesheets=[dbc.themes.SKETCHY])
server = app.server

app.layout = [
    html.H1("Storyboard generation"),
    dbc.Card(children=[dbc.CardHeader("Settings"),
             dbc.CardBody(children=[
                html.Table(style={"padding": "10px", "border-collapse": "collapse"}, children=[

                    html.Tr(children=[
                        html.Td(style={"width": "100px", "padding": "10px"},
                                children=[html.Label(children="Image model")]),

                        html.Td(style={"width": "400px", "padding": "10px"},
                                children=[dcc.Dropdown(options=[{"label": i.value, "value": i.name} for i in ImageModel],
                                                       value=DEFAULT_IMAGE_MODEL.name, id="image_model", style={"width": "100%"},
                                                       clearable=False)]),
                        ]),

                    html.Tr(children=[
                        html.Td(style={"width": "100px", "padding": "10px"},
                                children=[html.Label(children="Number of steps")]),

                        html.Td(style={"width": "400px", "padding": "10px"},
                                children=[dcc.Input(id="nb_steps", type="number", min=1, max=30, step=1, style={"width": "100%"})]),
                        ]),

                    html.Tr(children=[
                        html.Td(style={"width": "100px", "padding": "10px"},
                                children=[html.Label(children="Image aspect ratio")]),

                        html.Td(style={"width": "400px", "padding": "10px"},
                                children=[dcc.Dropdown(options=[{"label": a.value, "value": a.name} for a in AspectRatio],
                                                       value=DEFAULT_ASPECT_RATIO.name, id="aspect_ratio", style={"width": "100%"},
                                                       clearable=False)]),
                        ]),

                    html.Tr(children=[
                        html.Td(style={"width": "100px", "padding": "10px"},
                                children=[html.Label(children="Style")]),

                        html.Td(style={"width": "400px", "padding": "10px"},
                                children=[dcc.Dropdown(options=[{"label": s.value, "value": s.name} for s in Style],
                                                       value=DEFAULT_STYLE.name, id="style", style={"width": "100%"},
                                                       clearable=False)]),
                        html.Td(children=[dcc.Input(id="style_description", style={"width": "750px", "display": "none"})])
                        ]),
                    ]),

                    html.Tr(children=[
                        html.Td(style={"width": "100px", "height": "100px", "padding": "10px", "vertical-align": "middle"},
                                children=[html.Label(children="Project description")]),

                        html.Td(style={"width": "1200px", "height": "100px", "padding": "10px"},
                                children=[dcc.Textarea(id="description", style={"width": "100%", "height": "100%"}, value="")]),
                        ]),

                    html.Tr(children=[
                        html.Td(style={"width": "100px", "height": "200px", "padding": "10px", "vertical-align": "middle"},
                                children=[html.Label(children="Insights to leverage in the story")]),

                        html.Td(style={"width": "1200px", "height": "200px", "padding": "10px"},
                                children=[dcc.Textarea(id="expectations", style={"width": "100%", "height": "100%"}, value="")]),
                        ]),

                    html.Tr(children=[
                        html.Td(style={"width": "100px", "height": "100px", "padding": "10px", "vertical-align": "middle"},
                                children=[html.Label(children="Terms to avoid in the prompt")]),

                        html.Td(style={"width": "1200px", "padding": "10px"},
                                children=[dcc.Input(id="input_term", value="", className="me-1"),
                                          html.Button(children="Add term", id="add_term", className="me-1"),
                                          dcc.Dropdown(options=[], value=None, id="avoided_terms", style={"width": "100%"}, multi=True)
                                          ])
                        ])
                 ])
              ], style={"width": "1300px"}
             ),
    html.Br(),
    html.Button(children="Generate", id="generate", className="me-1"),
    html.Button(children="Cancel", id="cancel", className="me-1", disabled=True),
    html.Button(children="Reset", id="reset"),
    html.Br(),
    html.Br(),
    dbc.Card(children=[html.P(children="Idle", id="status")], style={"vertical-align": "middle", "width": "1300px"}),
    html.Br(),
    dcc.Download(id="generated-file")
    ]


if __name__ == '__main__':
    app.run_server(debug=True, port=8050)

# -*- coding: utf-8 -*-
"""
Created on Fri Feb 21 15:14:49 2025

@author: kevguima
"""

import os
from openai import AsyncOpenAI
from typing import List, Union, Literal
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field
import requests
from io import BytesIO
from pptx import Presentation
from pptx.util import Cm, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
from pptx.dml.color import RGBColor
from pptx.oxml.xmlchemy import OxmlElement
from pptx.enum.dml import MSO_FILL
from enum import Enum
import asyncio


class ImageModel(Enum):
    OPENAI_DALLE_3 = "OpenAI DALL-e 3"


class AspectRatio(Enum):
    WIDESCREEN = "16:9"
    SQUARE = "1:1"
    VERTICAL = "9:16"


class Style(Enum):
    SKETCHY_BW_GRAPHIC = "Sketchy B&W Graphic"
    CARTOON = "Cartoon-like"
    REALISTIC = "Realistic pictures"
    CUSTOM = "Custom"


class Shot(Enum):
    FULL_SHOT = "Full shot"
    AMERICAN_SHOT = "American shot"
    MEDIUM_SHOT = "Medium shot"
    MEDIUM_CLOSEUP_SHOT = "Medium close-up shot"
    CLOSEUP_SHOT = "Close-up shot"


API_KEY = os.environ.get("OPENAI_KEY")

POOL_SIZE = 10


def SubElement(parent, tagname, **kwargs):
    element = OxmlElement(tagname)
    element.attrib.update(kwargs)
    parent.append(element)

    return element


def change_border_color(cell, border_color):
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()

    borders = ["a:lnL", "a:lnR", "a:lnT", "a:lnB"]

    if cell.fill.type == MSO_FILL.SOLID:
        fill_color = cell.fill.fore_color.rgb
    else:
        fill_color = RGBColor(0, 0, 0)

    cell.fill.background()

    for border in borders:
        ln = SubElement(tcPr, border)
        solidFill = SubElement(ln, 'a:solidFill')
        SubElement(solidFill, 'a:srgbClr', val=border_color)

    cell.fill.solid()
    cell.fill.fore_color.rgb = fill_color


class JourneyStep(BaseModel):
    step_name: str = Field(description="Step name like \"Select payment method\"")
    step_description: str = Field(description="Full description of the journey step")
    scene_description: str = Field(description="Description of the picture that could represent this step")
    photo_shooting: Literal[tuple(s.name for s in Shot)] = Field(description="Best shooting type")


class JourneyStage(BaseModel):
    stage_name: str = Field(description="Stage name like \"Delivery\"")
    steps: List[JourneyStep] = Field(description="Steps in the customer stage")


class JourneyStory(BaseModel):
    story_name: str = Field(description="Story name like \"Emily's Omnichannel DIY Adventure\"")
    persona_description: str = Field(description="Persona's description like \"Emily is a tech-savvy digital native who expects seamless interactions with brands\"")
    persona_physical_description: str = Field(description="Persona's detailed physical description like \"Jennifer is a dark-haired woman with glasses wearing a white shirt.\"")
    stages: List[JourneyStage] = Field(description="Stages in the customer journey")


def calculate_cell_position(table, row, col):
    table_gf = table._graphic_frame
    x = table_gf._element.xfrm.off.x
    y = table_gf._element.xfrm.off.y

    cell_left = x
    cell_top = y
    cell_height = table.rows[0].height
    cell_width = table.columns[0].width

    for r in range(row):
        cell_top += table.rows[r].height

    for c in range(col):
        cell_left += table.columns[c].width

    cell_height = table.rows[row].height
    cell_width = table.columns[col].width

    return cell_left, cell_top, cell_width, cell_height


async def generate_image(client: object, image_model: ImageModel, aspect_ratio: AspectRatio, prompt: str) -> BytesIO:
    if image_model == ImageModel.OPENAI_DALLE_3:
        if aspect_ratio == AspectRatio.SQUARE:
            image_size = "1024x1024"
        elif aspect_ratio == AspectRatio.WIDESCREEN:
            image_size = "1792x1024"
        elif aspect_ratio == AspectRatio.VERTICAL:
            image_size = "1024x1792"

        response = await client.images.generate(model="dall-e-3", size=image_size, prompt=prompt)

    http_response = requests.get(response.data[0].url)

    buffer = BytesIO()

    buffer.write(http_response.content)

    return buffer


async def generate_storyboard(set_progress, description: str, expectations_list: List[str], image_model: ImageModel, nb_steps: Union[None, int], aspect_ratio: AspectRatio, style: Style, style_description_input: str, avoided_terms: List[str]) -> Presentation:
    if aspect_ratio == AspectRatio.SQUARE:
        image_ratio = 1
    elif aspect_ratio == AspectRatio.WIDESCREEN:
        image_ratio = 1.75
    elif aspect_ratio == AspectRatio.VERTICAL:
        image_ratio = 0.57

    if style == Style.SKETCHY_BW_GRAPHIC:
        style_description = "Sketchy graphic style, quick, rough and imprecise marker strokes, not too detailed, with few strokes, white background, black color for lines and a light gray scale for shades."
    elif style == Style.CARTOON:
        style_description = "Cartoon-like images."
    elif style == Style.REALISTIC:
        style_description = "Photo-realistic pictures."
    elif style == Style.CUSTOM:
        style_description = style_description_input

    instructions = "Build a story from the need realisation to the final use of the product at home. You'll provide a customer journey composed of a list of stages, each stage being composed of one or several steps."

    if nb_steps:
        instructions += f" Only make {nb_steps} steps in total."

    if avoided_terms:
        instructions += " Avoid the following terms in the description of steps: {}.".format("'" + "', '".join(avoided_terms) + "'")

    model = ChatOpenAI(api_key=API_KEY,
                       model="o1",
                       timeout=120)

    if expectations_list:
        expectations = "Insert in the story elements coming from a list of standard expectations:\n" + \
                       "\n".join([f"- {e}" for e in expectations_list])
    else:
        expectations = ""

    prompt = "\n".join([description, instructions, expectations])

    struct_model = model.with_structured_output(JourneyStory)

    set_progress("Story generation...")
    story = struct_model.invoke(prompt)

    prs = Presentation()
    prs.slide_width = Cm(36)
    prs.slide_height = Cm(18)

    blank_slide_layout = prs.slide_layouts[6]
    slide = prs.slides.add_slide(blank_slide_layout)
    shapes = slide.shapes

    slide.background.fill.solid()
    slide.background.fill.fore_color.rgb = RGBColor(0xF7, 0xF7, 0xF7)

    cols = sum([len(stage.steps) for stage in story.stages])
    rows = 4

    # Story title
    textbox = shapes.add_textbox(Cm(1.5), Cm(2), Cm(15), Cm(2.5))
    textbox.text = story.story_name
    textbox.text_frame.word_wrap = True
    textbox.text_frame.paragraphs[0].font.name = "Futura Next"
    textbox.text_frame.paragraphs[0].font.bold = True
    textbox.text_frame.paragraphs[0].font.size = Pt(24)
    textbox.text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE

    # Persona description
    img_left = Cm(20)
    img_top = Cm(1.5)
    img_size = Cm(3)

    img_width = img_size * image_ratio
    img_height = img_size
    img_left -= (img_width - img_size)

    shape = shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE, Cm(19.7) - (img_width - img_size), Cm(1.2), Cm(14.5) + (img_width - img_size), Cm(3.5))
    shape.fill.solid()
    shape.fill.fore_color.rgb = RGBColor(255, 255, 255)
    shape.line.color.rgb = RGBColor(255, 255, 255)
    shape.adjustments[0] = 0.04

    textbox = shapes.add_textbox(Cm(23), Cm(1.5), Cm(11), Cm(3))
    textbox.text = story.persona_description
    textbox.text_frame.word_wrap = True
    textbox.text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
    textbox.text_frame.paragraphs[0].font.size = Pt(10)
    textbox.text_frame.paragraphs[0].font.name = "Futura Next"

    set_progress("Persona generation...")
    client = AsyncOpenAI(api_key=API_KEY)

    prompt = story.persona_physical_description + " " + style_description

    buffer = await generate_image(client, image_model, aspect_ratio, prompt)

    shapes.add_picture(buffer, img_left, img_top, img_width, img_height)

    # Table of steps
    img_cell_height = Cm(5.5)
    img_height = min(int(Cm(34) / cols / image_ratio), img_cell_height)
    img_width = image_ratio * img_height
    table_height = Cm(0.5) + Cm(1) + img_height + Cm(3.5)

    shape = shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE, Cm(1), Cm(6), Cm(34), table_height + Cm(1))
    shape.fill.solid()
    shape.fill.fore_color.rgb = RGBColor(0x23, 0x01, 0x01)
    shape.adjustments[0] = 0.04

    table = shapes.add_table(rows, cols, Cm(1.5), Cm(6), Cm(33), table_height).table

    table.rows[0].height = Cm(0.5)
    table.rows[1].height = Cm(1)
    table.rows[2].height = img_height
    table.rows[3].height = Cm(3.5)

    idx_step = 0
    for stage in story.stages:
        cell = table.cell(0, idx_step)

        if len(stage.steps) > 1:
            cell.merge(table.cell(0, idx_step + len(stage.steps) - 1))

        cell.text = stage.stage_name
        cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        cell.text_frame.paragraphs[0].font.bold = False
        cell.text_frame.paragraphs[0].font.size = Pt(10)
        cell.text_frame.paragraphs[0].font.name = "Futura Next"
        cell.text_frame.paragraphs[0].font.fill.solid()
        cell.text_frame.paragraphs[0].font.fill.fore_color.rgb = RGBColor(255, 255, 255)
        cell.fill.solid()
        cell.fill.fore_color.rgb = RGBColor(0x23, 0x01, 0x01)

        for j, step in enumerate(stage.steps):
            cell = table.cell(1, idx_step + j)
            cell.text = step.step_name
            cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
            cell.text_frame.paragraphs[0].font.bold = False
            cell.text_frame.paragraphs[0].font.size = Pt(11)
            cell.text_frame.paragraphs[0].font.name = "Futura Next"
            cell.text_frame.paragraphs[0].font.fill.solid()
            cell.text_frame.paragraphs[0].font.fill.fore_color.rgb = RGBColor(255, 255, 255)
            cell.fill.solid()
            cell.fill.fore_color.rgb = RGBColor(0x6C, 0x0A, 0x0A)

            cell = table.cell(2, idx_step + j)
            cell.fill.solid()
            cell.fill.fore_color.rgb = RGBColor(0x23, 0x01, 0x01)

            cell = table.cell(3, idx_step + j)
            cell.text = step.step_description
            cell.text_frame.paragraphs[0].font.size = Pt(9)
            cell.text_frame.paragraphs[0].font.name = "Futura Next"
            cell.fill.solid()
            cell.fill.fore_color.rgb = RGBColor(0xEE, 0xEE, 0xEE)

        idx_step += len(stage.steps)

    for row in table.rows:
        for cell in row.cells:
            change_border_color(cell, "230101")

    # Scenes generation
    img_prompts = []

    for stage in story.stages:
        for step in stage.steps:
            prompt = " ".join([f"Composition: {Shot[step.photo_shooting].value}.",
                               style_description,
                               story.persona_physical_description,
                               step.scene_description])
            img_prompts.append(prompt)

    set_progress(f"Scenes generation... 0/{cols}")
    tasks = []
    buffers = []

    for col in range(cols):
        task = asyncio.create_task(generate_image(client, image_model, aspect_ratio, img_prompts[col]))
        tasks.append(task)

    for i in range(0, len(tasks), POOL_SIZE):
        pool = tasks[i:i + POOL_SIZE]

        await asyncio.gather(*pool)

        for task in pool:
            buffers.append(task.result())

        set_progress(f"Scenes generation... {i + len(pool)}/{cols}")

    for col, buffer in enumerate(buffers):
        cell_left, cell_top, cell_width, cell_height = calculate_cell_position(table, 2, col)

        cell_top = Cm(7.88)

        img_height = min(cell_width / image_ratio, cell_height)
        img_width = image_ratio * img_height

        cell_left += (cell_width - img_width) / 2
        cell_top += (cell_height - img_height) / 2

        # Handling of table borders
        cell_left += Cm(0.02)
        img_width -= Cm(0.04)

        shapes.add_picture(buffer, cell_left, cell_top, cell_width, cell_width / image_ratio)

    return prs

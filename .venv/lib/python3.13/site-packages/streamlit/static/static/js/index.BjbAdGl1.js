import{d as Ze,bv as J,r as C,b$ as vt,bO as mt,bw as G,c0 as bt,c1 as yt,s as me,J as Tt,bF as kt,z as Ot,aO as wt,C as Ve,j as re,cr as Rt,br as St,bH as Mt,bs as _t,b9 as $t,bt as Et,S as It,cs as $e}from"./index.BTGIlECR.js";import{s as ze}from"./sprintf.D7DtBTRn.js";import{a as xt}from"./useBasicWidgetState.oPxte5Mj.js";import"./FormClearHelper.DuzI-rET.js";var se={},H={},ue={},ce={},Ne;function Ie(){if(Ne)return ce;Ne=1,Object.defineProperty(ce,"__esModule",{value:!0}),ce.Direction=void 0;var e;return function(r){r.Right="to right",r.Left="to left",r.Down="to bottom",r.Up="to top"}(e||(ce.Direction=e={})),ce}var Ue;function Ge(){return Ue||(Ue=1,function(e){var r=ue&&ue.__spreadArray||function(i,o,u){if(u||arguments.length===2)for(var v=0,O=o.length,p;v<O;v++)(p||!(v in o))&&(p||(p=Array.prototype.slice.call(o,0,v)),p[v]=o[v]);return i.concat(p||Array.prototype.slice.call(o))};Object.defineProperty(e,"__esModule",{value:!0}),e.isIOS=e.useThumbOverlap=e.assertUnreachable=e.voidFn=e.getTrackBackground=e.replaceAt=e.schd=e.translate=e.getClosestThumbIndex=e.translateThumbs=e.getPaddingAndBorder=e.getMargin=e.checkInitialOverlap=e.checkValuesAgainstBoundaries=e.checkBoundaries=e.isVertical=e.relativeValue=e.normalizeValue=e.isStepDivisible=e.isTouchEvent=e.getStepDecimals=void 0;var a=Ze(),s=Ie(),h=function(i){var o=i.toString().split(".")[1];return o?o.length:0};e.getStepDecimals=h;function S(i){return i.touches&&i.touches.length||i.changedTouches&&i.changedTouches.length}e.isTouchEvent=S;function c(i,o,u){var v=(o-i)/u,O=8,p=Number(v.toFixed(O));return parseInt(p.toString(),10)===p}e.isStepDivisible=c;function f(i,o,u,v,O,p,k){var _=1e11;if(i=Math.round(i*_)/_,!p){var P=k[o-1],x=k[o+1];if(P&&P>i)return P;if(x&&x<i)return x}if(i>v)return v;if(i<u)return u;var U=Math.floor(i*_-u*_)%Math.floor(O*_),z=Math.floor(i*_-Math.abs(U)),W=U===0?i:z/_,B=Math.abs(U/_)<O/2?W:W+O,A=(0,e.getStepDecimals)(O);return parseFloat(B.toFixed(A))}e.normalizeValue=f;function j(i,o,u){return(i-o)/(u-o)}e.relativeValue=j;function F(i){return i===s.Direction.Up||i===s.Direction.Down}e.isVertical=F;function L(i,o,u){if(o>=u)throw new RangeError("min (".concat(o,") is equal/bigger than max (").concat(u,")"));if(i<o)throw new RangeError("value (".concat(i,") is smaller than min (").concat(o,")"));if(i>u)throw new RangeError("value (".concat(i,") is bigger than max (").concat(u,")"))}e.checkBoundaries=L;function $(i,o,u){return i<o?o:i>u?u:i}e.checkValuesAgainstBoundaries=$;function b(i){if(!(i.length<2)&&!i.slice(1).every(function(o,u){return i[u]<=o}))throw new RangeError("values={[".concat(i,"]} needs to be sorted when allowOverlap={false}"))}e.checkInitialOverlap=b;function d(i){var o=window.getComputedStyle(i);return{top:parseInt(o["margin-top"],10),bottom:parseInt(o["margin-bottom"],10),left:parseInt(o["margin-left"],10),right:parseInt(o["margin-right"],10)}}e.getMargin=d;function t(i){var o=window.getComputedStyle(i);return{top:parseInt(o["padding-top"],10)+parseInt(o["border-top-width"],10),bottom:parseInt(o["padding-bottom"],10)+parseInt(o["border-bottom-width"],10),left:parseInt(o["padding-left"],10)+parseInt(o["border-left-width"],10),right:parseInt(o["padding-right"],10)+parseInt(o["border-right-width"],10)}}e.getPaddingAndBorder=t;function n(i,o,u){var v=u?-1:1;i.forEach(function(O,p){return g(O,v*o[p].x,o[p].y)})}e.translateThumbs=n;function l(i,o,u,v){for(var O=0,p=D(i[0],o,u,v),k=1;k<i.length;k++){var _=D(i[k],o,u,v);_<p&&(p=_,O=k)}return O}e.getClosestThumbIndex=l;function g(i,o,u){i.style.transform="translate(".concat(o,"px, ").concat(u,"px)")}e.translate=g;var m=function(i){var o=[],u=null,v=function(){for(var O=[],p=0;p<arguments.length;p++)O[p]=arguments[p];o=O,!u&&(u=requestAnimationFrame(function(){u=null,i.apply(void 0,o)}))};return v};e.schd=m;function y(i,o,u){var v=i.slice(0);return v[o]=u,v}e.replaceAt=y;function E(i){var o=i.values,u=i.colors,v=i.min,O=i.max,p=i.direction,k=p===void 0?s.Direction.Right:p,_=i.rtl,P=_===void 0?!1:_;P&&k===s.Direction.Right?k=s.Direction.Left:P&&s.Direction.Left&&(k=s.Direction.Right);var x=o.slice(0).sort(function(z,W){return z-W}).map(function(z){return(z-v)/(O-v)*100}),U=x.reduce(function(z,W,B){return"".concat(z,", ").concat(u[B]," ").concat(W,"%, ").concat(u[B+1]," ").concat(W,"%")},"");return"linear-gradient(".concat(k,", ").concat(u[0]," 0%").concat(U,", ").concat(u[u.length-1]," 100%)")}e.getTrackBackground=E;function w(){}e.voidFn=w;function R(i){throw new Error("Didn't expect to get here")}e.assertUnreachable=R;var M=function(i,o,u,v,O){O===void 0&&(O=function(k){return k});var p=Math.ceil(r([i],Array.from(i.children),!0).reduce(function(k,_){var P=Math.ceil(_.getBoundingClientRect().width);if(_.innerText&&_.innerText.includes(u)&&_.childElementCount===0){var x=_.cloneNode(!0);x.innerHTML=O(o.toFixed(v)),x.style.visibility="hidden",document.body.appendChild(x),P=Math.ceil(x.getBoundingClientRect().width),document.body.removeChild(x)}return P>k?P:k},i.getBoundingClientRect().width));return p},I=function(i,o,u,v,O,p,k){k===void 0&&(k=function(x){return x});var _=[],P=function(x){var U=M(u[x],v[x],O,p,k),z=o[x].x;o.forEach(function(W,B){var A=W.x,Z=M(u[B],v[B],O,p,k);x!==B&&(z>=A&&z<=A+Z||z+U>=A&&z+U<=A+Z)&&(_.includes(B)||(_.push(x),_.push(B),_=r(r([],_,!0),[x,B],!1),P(B)))})};return P(i),Array.from(new Set(_.sort()))},T=function(i,o,u,v,O,p){v===void 0&&(v=.1),O===void 0&&(O=" - "),p===void 0&&(p=function(B){return B});var k=(0,e.getStepDecimals)(v),_=(0,a.useState)({}),P=_[0],x=_[1],U=(0,a.useState)(p(o[u].toFixed(k))),z=U[0],W=U[1];return(0,a.useEffect)(function(){if(i){var B=i.getThumbs();if(B.length<1)return;var A={},Z=i.getOffsets(),ne=I(u,Z,B,o,O,k,p),le=p(o[u].toFixed(k));if(ne.length){var Q=ne.reduce(function(ee,oe,ge,pe){return ee.length?r(r([],ee,!0),[Z[pe[ge]].x],!1):[Z[pe[ge]].x]},[]);if(Math.min.apply(Math,Q)===Z[u].x){var de=[];ne.forEach(function(ee){de.push(o[ee].toFixed(k))}),le=Array.from(new Set(de.sort(function(ee,oe){return parseFloat(ee)-parseFloat(oe)}))).map(p).join(O);var fe=Math.min.apply(Math,Q),he=Math.max.apply(Math,Q),_e=B[ne[Q.indexOf(he)]].getBoundingClientRect().width;A.left="".concat(Math.abs(fe-(he+_e))/2,"px"),A.transform="translate(-50%, 0)"}else A.visibility="hidden"}W(le),x(A)}},[i,o]),[z,P]};e.useThumbOverlap=T;function D(i,o,u,v){var O=i.getBoundingClientRect(),p=O.left,k=O.top,_=O.width,P=O.height;return F(v)?Math.abs(u-(k+P/2)):Math.abs(o-(p+_/2))}var V=function(){var i,o=((i=navigator.userAgentData)===null||i===void 0?void 0:i.platform)||navigator.platform;return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(o)||navigator.userAgent.includes("Mac")&&"ontouchend"in document};e.isIOS=V}(ue)),ue}var He;function Ct(){if(He)return H;He=1;var e=H&&H.__extends||function(){var $=function(b,d){return $=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(t[l]=n[l])},$(b,d)};return function(b,d){if(typeof d!="function"&&d!==null)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");$(b,d);function t(){this.constructor=b}b.prototype=d===null?Object.create(d):(t.prototype=d.prototype,new t)}}(),r=H&&H.__createBinding||(Object.create?function($,b,d,t){t===void 0&&(t=d);var n=Object.getOwnPropertyDescriptor(b,d);(!n||("get"in n?!b.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return b[d]}}),Object.defineProperty($,t,n)}:function($,b,d,t){t===void 0&&(t=d),$[t]=b[d]}),a=H&&H.__setModuleDefault||(Object.create?function($,b){Object.defineProperty($,"default",{enumerable:!0,value:b})}:function($,b){$.default=b}),s=H&&H.__importStar||function($){if($&&$.__esModule)return $;var b={};if($!=null)for(var d in $)d!=="default"&&Object.prototype.hasOwnProperty.call($,d)&&r(b,$,d);return a(b,$),b},h=H&&H.__spreadArray||function($,b,d){if(d||arguments.length===2)for(var t=0,n=b.length,l;t<n;t++)(l||!(t in b))&&(l||(l=Array.prototype.slice.call(b,0,t)),l[t]=b[t]);return $.concat(l||Array.prototype.slice.call(b))};Object.defineProperty(H,"__esModule",{value:!0});var S=s(Ze()),c=Ge(),f=Ie(),j=["ArrowRight","ArrowUp","k","PageUp"],F=["ArrowLeft","ArrowDown","j","PageDown"],L=function($){e(b,$);function b(d){var t=$.call(this,d)||this;if(t.trackRef=S.createRef(),t.thumbRefs=[],t.state={draggedTrackPos:[-1,-1],draggedThumbIndex:-1,thumbZIndexes:new Array(t.props.values.length).fill(0).map(function(n,l){return l}),isChanged:!1,markOffsets:[]},t.getOffsets=function(){var n=t.props,l=n.direction,g=n.values,m=n.min,y=n.max,E=t.trackRef.current;if(!E)return console.warn("No track element found."),[];var w=E.getBoundingClientRect(),R=(0,c.getPaddingAndBorder)(E);return t.getThumbs().map(function(M,I){var T={x:0,y:0},D=M.getBoundingClientRect(),V=(0,c.getMargin)(M);switch(l){case f.Direction.Right:return T.x=(V.left+R.left)*-1,T.y=((D.height-w.height)/2+R.top)*-1,T.x+=w.width*(0,c.relativeValue)(g[I],m,y)-D.width/2,T;case f.Direction.Left:return T.x=(V.right+R.right)*-1,T.y=((D.height-w.height)/2+R.top)*-1,T.x+=w.width-w.width*(0,c.relativeValue)(g[I],m,y)-D.width/2,T;case f.Direction.Up:return T.x=((D.width-w.width)/2+V.left+R.left)*-1,T.y=-R.left,T.y+=w.height-w.height*(0,c.relativeValue)(g[I],m,y)-D.height/2,T;case f.Direction.Down:return T.x=((D.width-w.width)/2+V.left+R.left)*-1,T.y=-R.left,T.y+=w.height*(0,c.relativeValue)(g[I],m,y)-D.height/2,T;default:return(0,c.assertUnreachable)(l)}})},t.getThumbs=function(){return t.trackRef&&t.trackRef.current?Array.from(t.trackRef.current.children).filter(function(n){return n.hasAttribute("aria-valuenow")}):(console.warn("No thumbs found in the track container. Did you forget to pass & spread the `props` param in renderTrack?"),[])},t.getTargetIndex=function(n){return t.getThumbs().findIndex(function(l){return l===n.target||l.contains(n.target)})},t.addTouchEvents=function(n){document.addEventListener("touchmove",t.schdOnTouchMove,{passive:!1}),document.addEventListener("touchend",t.schdOnEnd,{passive:!1}),document.addEventListener("touchcancel",t.schdOnEnd,{passive:!1})},t.addMouseEvents=function(n){document.addEventListener("mousemove",t.schdOnMouseMove),document.addEventListener("mouseup",t.schdOnEnd)},t.onMouseDownTrack=function(n){var l;if(!(n.button!==0||(0,c.isIOS)()))if(n.persist(),n.preventDefault(),t.addMouseEvents(n.nativeEvent),t.props.values.length>1&&t.props.draggableTrack){if(t.thumbRefs.some(function(m){var y;return(y=m.current)===null||y===void 0?void 0:y.contains(n.target)}))return;t.setState({draggedTrackPos:[n.clientX,n.clientY]},function(){return t.onMove(n.clientX,n.clientY)})}else{var g=(0,c.getClosestThumbIndex)(t.thumbRefs.map(function(m){return m.current}),n.clientX,n.clientY,t.props.direction);(l=t.thumbRefs[g].current)===null||l===void 0||l.focus(),t.setState({draggedThumbIndex:g},function(){return t.onMove(n.clientX,n.clientY)})}},t.onResize=function(){(0,c.translateThumbs)(t.getThumbs(),t.getOffsets(),t.props.rtl),t.calculateMarkOffsets()},t.onTouchStartTrack=function(n){var l;if(n.persist(),t.addTouchEvents(n.nativeEvent),t.props.values.length>1&&t.props.draggableTrack){if(t.thumbRefs.some(function(m){var y;return(y=m.current)===null||y===void 0?void 0:y.contains(n.target)}))return;t.setState({draggedTrackPos:[n.touches[0].clientX,n.touches[0].clientY]},function(){return t.onMove(n.touches[0].clientX,n.touches[0].clientY)})}else{var g=(0,c.getClosestThumbIndex)(t.thumbRefs.map(function(m){return m.current}),n.touches[0].clientX,n.touches[0].clientY,t.props.direction);(l=t.thumbRefs[g].current)===null||l===void 0||l.focus(),t.setState({draggedThumbIndex:g},function(){return t.onMove(n.touches[0].clientX,n.touches[0].clientY)})}},t.onMouseOrTouchStart=function(n){if(!t.props.disabled){var l=(0,c.isTouchEvent)(n);if(!(!l&&n.button!==0)){var g=t.getTargetIndex(n);g!==-1&&(l?t.addTouchEvents(n):t.addMouseEvents(n),t.setState({draggedThumbIndex:g,thumbZIndexes:t.state.thumbZIndexes.map(function(m,y){return y===g?Math.max.apply(Math,t.state.thumbZIndexes):m<=t.state.thumbZIndexes[g]?m:m-1})}))}}},t.onMouseMove=function(n){n.preventDefault(),t.onMove(n.clientX,n.clientY)},t.onTouchMove=function(n){n.preventDefault(),t.onMove(n.touches[0].clientX,n.touches[0].clientY)},t.onKeyDown=function(n){var l=t.props,g=l.values,m=l.onChange,y=l.step,E=l.rtl,w=l.direction,R=t.state.isChanged,M=t.getTargetIndex(n.nativeEvent),I=E||w===f.Direction.Left||w===f.Direction.Down?-1:1;M!==-1&&(j.includes(n.key)?(n.preventDefault(),t.setState({draggedThumbIndex:M,isChanged:!0}),m((0,c.replaceAt)(g,M,t.normalizeValue(g[M]+I*(n.key==="PageUp"?y*10:y),M)))):F.includes(n.key)?(n.preventDefault(),t.setState({draggedThumbIndex:M,isChanged:!0}),m((0,c.replaceAt)(g,M,t.normalizeValue(g[M]-I*(n.key==="PageDown"?y*10:y),M)))):n.key==="Tab"?t.setState({draggedThumbIndex:-1},function(){R&&t.fireOnFinalChange()}):R&&t.fireOnFinalChange())},t.onKeyUp=function(n){var l=t.state.isChanged;t.setState({draggedThumbIndex:-1},function(){l&&t.fireOnFinalChange()})},t.onMove=function(n,l){var g=t.state,m=g.draggedThumbIndex,y=g.draggedTrackPos,E=t.props,w=E.direction,R=E.min,M=E.max,I=E.onChange,T=E.values,D=E.step,V=E.rtl;if(m===-1&&y[0]===-1&&y[1]===-1)return null;var i=t.trackRef.current;if(!i)return null;var o=i.getBoundingClientRect(),u=(0,c.isVertical)(w)?o.height:o.width;if(y[0]!==-1&&y[1]!==-1){var v=n-y[0],O=l-y[1],p=0;switch(w){case f.Direction.Right:case f.Direction.Left:p=v/u*(M-R);break;case f.Direction.Down:case f.Direction.Up:p=O/u*(M-R);break;default:(0,c.assertUnreachable)(w)}if(V&&(p*=-1),Math.abs(p)>=D/2){for(var k=0;k<t.thumbRefs.length;k++){if(T[k]===M&&Math.sign(p)===1||T[k]===R&&Math.sign(p)===-1)return;var _=T[k]+p;_>M?p=M-T[k]:_<R&&(p=R-T[k])}for(var P=T.slice(0),k=0;k<t.thumbRefs.length;k++)P=(0,c.replaceAt)(P,k,t.normalizeValue(T[k]+p,k));t.setState({draggedTrackPos:[n,l]}),I(P)}}else{var x=0;switch(w){case f.Direction.Right:x=(n-o.left)/u*(M-R)+R;break;case f.Direction.Left:x=(u-(n-o.left))/u*(M-R)+R;break;case f.Direction.Down:x=(l-o.top)/u*(M-R)+R;break;case f.Direction.Up:x=(u-(l-o.top))/u*(M-R)+R;break;default:(0,c.assertUnreachable)(w)}V&&(x=M+R-x),Math.abs(T[m]-x)>=D/2&&I((0,c.replaceAt)(T,m,t.normalizeValue(x,m)))}},t.normalizeValue=function(n,l){var g=t.props,m=g.min,y=g.max,E=g.step,w=g.allowOverlap,R=g.values;return(0,c.normalizeValue)(n,l,m,y,E,w,R)},t.onEnd=function(n){if(n.preventDefault(),document.removeEventListener("mousemove",t.schdOnMouseMove),document.removeEventListener("touchmove",t.schdOnTouchMove),document.removeEventListener("mouseup",t.schdOnEnd),document.removeEventListener("touchend",t.schdOnEnd),document.removeEventListener("touchcancel",t.schdOnEnd),t.state.draggedThumbIndex===-1&&t.state.draggedTrackPos[0]===-1&&t.state.draggedTrackPos[1]===-1)return null;t.setState({draggedThumbIndex:-1,draggedTrackPos:[-1,-1]},function(){t.fireOnFinalChange()})},t.fireOnFinalChange=function(){t.setState({isChanged:!1});var n=t.props,l=n.onFinalChange,g=n.values;l&&l(g)},t.updateMarkRefs=function(n){if(!n.renderMark){t.numOfMarks=void 0,t.markRefs=void 0;return}t.numOfMarks=(n.max-n.min)/t.props.step,t.markRefs=[];for(var l=0;l<t.numOfMarks+1;l++)t.markRefs[l]=S.createRef()},t.calculateMarkOffsets=function(){if(!(!t.props.renderMark||!t.trackRef||!t.numOfMarks||!t.markRefs||t.trackRef.current===null)){for(var n=window.getComputedStyle(t.trackRef.current),l=parseInt(n.width,10),g=parseInt(n.height,10),m=parseInt(n.paddingLeft,10),y=parseInt(n.paddingTop,10),E=[],w=0;w<t.numOfMarks+1;w++){var R=9999,M=9999;if(t.markRefs[w].current){var I=t.markRefs[w].current.getBoundingClientRect();R=I.height,M=I.width}t.props.direction===f.Direction.Left||t.props.direction===f.Direction.Right?E.push([Math.round(l/t.numOfMarks*w+m-M/2),-Math.round((R-g)/2)]):E.push([Math.round(g/t.numOfMarks*w+y-R/2),-Math.round((M-l)/2)])}t.setState({markOffsets:E})}},d.step===0)throw new Error('"step" property should be a positive number');return t.schdOnMouseMove=(0,c.schd)(t.onMouseMove),t.schdOnTouchMove=(0,c.schd)(t.onTouchMove),t.schdOnEnd=(0,c.schd)(t.onEnd),t.thumbRefs=d.values.map(function(){return S.createRef()}),t.updateMarkRefs(d),t}return b.prototype.componentDidMount=function(){var d=this,t=this.props,n=t.values,l=t.min,g=t.step;this.resizeObserver=window.ResizeObserver?new window.ResizeObserver(this.onResize):{observe:function(){return window.addEventListener("resize",d.onResize)},unobserve:function(){return window.removeEventListener("resize",d.onResize)}},document.addEventListener("touchstart",this.onMouseOrTouchStart,{passive:!1}),document.addEventListener("mousedown",this.onMouseOrTouchStart,{passive:!1}),!this.props.allowOverlap&&(0,c.checkInitialOverlap)(this.props.values),this.props.values.forEach(function(m){return(0,c.checkBoundaries)(m,d.props.min,d.props.max)}),this.resizeObserver.observe(this.trackRef.current),(0,c.translateThumbs)(this.getThumbs(),this.getOffsets(),this.props.rtl),this.calculateMarkOffsets(),n.forEach(function(m){(0,c.isStepDivisible)(l,m,g)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")})},b.prototype.componentDidUpdate=function(d,t){var n=this.props,l=n.max,g=n.min,m=n.step,y=n.values,E=n.rtl;(d.max!==l||d.min!==g||d.step!==m)&&this.updateMarkRefs(this.props),(0,c.translateThumbs)(this.getThumbs(),this.getOffsets(),E),(d.max!==l||d.min!==g||d.step!==m||t.markOffsets.length!==this.state.markOffsets.length)&&(this.calculateMarkOffsets(),y.forEach(function(w){(0,c.isStepDivisible)(g,w,m)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")}))},b.prototype.componentWillUnmount=function(){var d={passive:!1};document.removeEventListener("mousedown",this.onMouseOrTouchStart,d),document.removeEventListener("mousemove",this.schdOnMouseMove),document.removeEventListener("touchmove",this.schdOnTouchMove),document.removeEventListener("touchstart",this.onMouseOrTouchStart),document.removeEventListener("mouseup",this.schdOnEnd),document.removeEventListener("touchend",this.schdOnEnd),this.resizeObserver.unobserve(this.trackRef.current)},b.prototype.render=function(){var d=this,t=this.props,n=t.label,l=t.labelledBy,g=t.renderTrack,m=t.renderThumb,y=t.renderMark,E=y===void 0?function(){return null}:y,w=t.values,R=t.min,M=t.max,I=t.allowOverlap,T=t.disabled,D=this.state,V=D.draggedThumbIndex,i=D.thumbZIndexes,o=D.markOffsets;return g({props:{style:{transform:"scale(1)",cursor:V>-1?"grabbing":this.props.draggableTrack?(0,c.isVertical)(this.props.direction)?"ns-resize":"ew-resize":w.length===1&&!T?"pointer":"inherit"},onMouseDown:T?c.voidFn:this.onMouseDownTrack,onTouchStart:T?c.voidFn:this.onTouchStartTrack,ref:this.trackRef},isDragged:this.state.draggedThumbIndex>-1,disabled:T,children:h(h([],o.map(function(u,v,O){return E({props:{style:d.props.direction===f.Direction.Left||d.props.direction===f.Direction.Right?{position:"absolute",left:"".concat(u[0],"px"),marginTop:"".concat(u[1],"px")}:{position:"absolute",top:"".concat(u[0],"px"),marginLeft:"".concat(u[1],"px")},key:"mark".concat(v),ref:d.markRefs[v]},index:v})}),!0),w.map(function(u,v){var O=d.state.draggedThumbIndex===v;return m({index:v,value:u,isDragged:O,props:{style:{position:"absolute",zIndex:i[v],cursor:T?"inherit":O?"grabbing":"grab",userSelect:"none",touchAction:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none"},key:v,tabIndex:T?void 0:0,"aria-valuemax":I?M:w[v+1]||M,"aria-valuemin":I?R:w[v-1]||R,"aria-valuenow":u,draggable:!1,ref:d.thumbRefs[v],"aria-label":n,"aria-labelledby":l,role:"slider",onKeyDown:T?c.voidFn:d.onKeyDown,onKeyUp:T?c.voidFn:d.onKeyUp}})}),!0)})},b.defaultProps={label:"Accessibility label",labelledBy:null,step:1,direction:f.Direction.Right,rtl:!1,disabled:!1,allowOverlap:!1,draggableTrack:!1,min:0,max:100},b}(S.Component);return H.default=L,H}var We;function Dt(){return We||(We=1,function(e){var r=se&&se.__importDefault||function(S){return S&&S.__esModule?S:{default:S}};Object.defineProperty(e,"__esModule",{value:!0}),e.checkValuesAgainstBoundaries=e.relativeValue=e.useThumbOverlap=e.Direction=e.getTrackBackground=e.Range=void 0;var a=r(Ct());e.Range=a.default;var s=Ge();Object.defineProperty(e,"getTrackBackground",{enumerable:!0,get:function(){return s.getTrackBackground}}),Object.defineProperty(e,"useThumbOverlap",{enumerable:!0,get:function(){return s.useThumbOverlap}}),Object.defineProperty(e,"relativeValue",{enumerable:!0,get:function(){return s.relativeValue}}),Object.defineProperty(e,"checkValuesAgainstBoundaries",{enumerable:!0,get:function(){return s.checkValuesAgainstBoundaries}});var h=Ie();Object.defineProperty(e,"Direction",{enumerable:!0,get:function(){return h.Direction}})}(se)),se}var Je=Dt();function qe(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(h){return Object.getOwnPropertyDescriptor(e,h).enumerable})),a.push.apply(a,s)}return a}function ve(e){for(var r=1;r<arguments.length;r++){var a=arguments[r]!=null?arguments[r]:{};r%2?qe(Object(a),!0).forEach(function(s){Pt(e,s,a[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):qe(Object(a)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(a,s))})}return e}function Pt(e,r,a){return r in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}var be=J("div",{position:"relative",width:"100%"});be.displayName="Root";be.displayName="Root";be.displayName="StyledRoot";var ye=J("div",function(e){var r=e.$theme,a=e.$value,s=a===void 0?[]:a,h=e.$disabled,S=e.$isDragged,c=r.sizing,f="inherit";return h?f="not-allowed":S?f="grabbing":s.length===1&&(f="pointer"),{paddingTop:c.scale600,paddingBottom:c.scale600,paddingRight:c.scale600,paddingLeft:c.scale600,display:"flex",cursor:f,backgroundColor:r.colors.sliderTrackFill}});ye.displayName="Track";ye.displayName="Track";ye.displayName="StyledTrack";var Te=J("div",function(e){var r=e.$theme,a=e.$value,s=a===void 0?[]:a,h=e.$min,S=e.$max,c=e.$disabled,f=r.colors,j=r.borders,F=r.direction,L=r.borders.useRoundedCorners?j.radius100:0;return{borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,background:Je.getTrackBackground({values:s,colors:s.length===1?[c?f.borderOpaque:f.primary,c?f.backgroundSecondary:f.borderOpaque]:[c?f.backgroundSecondary:f.borderOpaque,c?f.borderOpaque:f.primary,c?f.backgroundSecondary:f.borderOpaque],min:h||0,max:S||0,rtl:F==="rtl"}),height:"2px",width:"100%",alignSelf:"center",cursor:c?"not-allowed":"inherit"}});Te.displayName="InnerTrack";Te.displayName="InnerTrack";Te.displayName="StyledInnerTrack";var ke=J("div",function(e){return{width:"4px",height:"2px",backgroundColor:e.$theme.colors.backgroundPrimary,marginLeft:"16px"}});ke.displayName="Mark";ke.displayName="Mark";ke.displayName="StyledMark";var Oe=J("div",function(e){return ve(ve({},e.$theme.typography.font200),{},{color:e.$theme.colors.contentPrimary})});Oe.displayName="Tick";Oe.displayName="Tick";Oe.displayName="StyledTick";var we=J("div",function(e){var r=e.$theme,a=r.sizing;return{display:"flex",justifyContent:"space-between",alignItems:"center",paddingRight:a.scale600,paddingLeft:a.scale600,paddingBottom:a.scale400}});we.displayName="TickBar";we.displayName="TickBar";we.displayName="StyledTickBar";var Re=J("div",function(e){var r=e.$theme,a=e.$value,s=a===void 0?[]:a,h=e.$thumbIndex,S=e.$disabled,c=s.length===2&&h===0,f=s.length===2&&h===1;return r.direction==="rtl"&&(f||c)&&(c=!c,f=!f),{height:"24px",width:"24px",borderTopLeftRadius:"24px",borderTopRightRadius:"24px",borderBottomLeftRadius:"24px",borderBottomRightRadius:"24px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:S?r.colors.sliderHandleFillDisabled:r.colors.sliderHandleFill,outline:"none",boxShadow:e.$isFocusVisible?"0 0 0 3px ".concat(r.colors.accent):"0 1px 4px rgba(0, 0, 0, 0.12)",cursor:S?"not-allowed":"inherit"}});Re.displayName="Thumb";Re.displayName="Thumb";Re.displayName="StyledThumb";var Se=J("div",function(e){var r=e.$disabled,a=e.$theme;return{position:"absolute",top:"-16px",width:"4px",height:"20px",backgroundColor:r?a.colors.sliderHandleFillDisabled:a.colors.sliderHandleInnerFill}});Se.displayName="InnerThumb";Se.displayName="InnerThumb";Se.displayName="StyledInnerThumb";var Me=J("div",function(e){var r=e.$disabled,a=e.$theme;return ve(ve({position:"absolute",top:"-".concat(a.sizing.scale1400)},a.typography.font200),{},{backgroundColor:r?a.colors.sliderHandleFillDisabled:a.colors.sliderHandleInnerFill,color:a.colors.contentInversePrimary,paddingLeft:a.sizing.scale600,paddingRight:a.sizing.scale600,paddingTop:a.sizing.scale500,paddingBottom:a.sizing.scale500,borderBottomLeftRadius:"48px",borderBottomRightRadius:"48px",borderTopLeftRadius:"48px",borderTopRightRadius:"48px",whiteSpace:"nowrap"})});Me.displayName="ThumbValue";Me.displayName="ThumbValue";Me.displayName="StyledThumbValue";function Ke(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(h){return Object.getOwnPropertyDescriptor(e,h).enumerable})),a.push.apply(a,s)}return a}function Bt(e){for(var r=1;r<arguments.length;r++){var a=arguments[r]!=null?arguments[r]:{};r%2?Ke(Object(a),!0).forEach(function(s){At(e,s,a[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Ke(Object(a)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(a,s))})}return e}function At(e,r,a){return r in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}function Y(){return Y=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e},Y.apply(this,arguments)}function K(e,r){return Vt(e)||jt(e,r)||Ft(e,r)||Lt()}function Lt(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ft(e,r){if(e){if(typeof e=="string")return Ye(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);if(a==="Object"&&e.constructor&&(a=e.constructor.name),a==="Map"||a==="Set")return Array.from(e);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return Ye(e,r)}}function Ye(e,r){(r==null||r>e.length)&&(r=e.length);for(var a=0,s=new Array(r);a<r;a++)s[a]=e[a];return s}function jt(e,r){var a=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(a!=null){var s=[],h=!0,S=!1,c,f;try{for(a=a.call(e);!(h=(c=a.next()).done)&&(s.push(c.value),!(r&&s.length===r));h=!0);}catch(j){S=!0,f=j}finally{try{!h&&a.return!=null&&a.return()}finally{if(S)throw f}}return s}}function Vt(e){if(Array.isArray(e))return e}var zt=function(r){if(r.length>2||r.length===0)throw new Error("the value prop represents positions of thumbs, so its length can be only one or two");return r};function Nt(e){var r=e.overrides,a=r===void 0?{}:r,s=e.disabled,h=s===void 0?!1:s,S=e.marks,c=S===void 0?!1:S,f=e.onChange,j=f===void 0?function(){}:f,F=e.onFinalChange,L=F===void 0?function(){}:F,$=e.min,b=$===void 0?0:$,d=e.max,t=d===void 0?100:d,n=e.step,l=n===void 0?1:n,g=e.persistentThumb,m=g===void 0?!1:g,y=e.valueToLabel,E=y===void 0?function(X){return X}:y,w=e.value,R=C.useContext(vt),M=C.useState(!1),I=K(M,2),T=I[0],D=I[1],V=C.useState(!1),i=K(V,2),o=i[0],u=i[1],v=C.useState(!1),O=K(v,2),p=O[0],k=O[1],_=C.useState(-1),P=K(_,2),x=P[0],U=P[1],z=C.useCallback(function(X){mt(X)&&k(!0);var N=X.target.parentNode.firstChild===X.target?0:1;U(N)},[]),W=C.useCallback(function(X){p!==!1&&k(!1),U(-1)},[]),B=zt(w),A={$disabled:h,$step:l,$min:b,$max:t,$marks:c,$value:B,$isFocusVisible:p},Z=G(a.Root,be),ne=K(Z,2),le=ne[0],Q=ne[1],de=G(a.Track,ye),fe=K(de,2),he=fe[0],_e=fe[1],ee=G(a.InnerTrack,Te),oe=K(ee,2),ge=oe[0],pe=oe[1],Qe=G(a.Thumb,Re),xe=K(Qe,2),et=xe[0],tt=xe[1],rt=G(a.InnerThumb,Se),Ce=K(rt,2),nt=Ce[0],at=Ce[1],it=G(a.ThumbValue,Me),De=K(it,2),ot=De[0],st=De[1],ut=G(a.Tick,Oe),Pe=K(ut,2),Be=Pe[0],Ae=Pe[1],ct=G(a.TickBar,we),Le=K(ct,2),lt=Le[0],dt=Le[1],ft=G(a.Mark,ke),Fe=K(ft,2),ht=Fe[0],gt=Fe[1];return C.createElement(le,Y({"data-baseweb":"slider"},A,Q,{onFocus:yt(Q,z),onBlur:bt(Q,W)}),C.createElement(Je.Range,Y({step:l,min:b,max:t,values:B,disabled:h,onChange:function(N){return j({value:N})},onFinalChange:function(N){return L({value:N})},rtl:R.direction==="rtl",renderTrack:function(N){var te=N.props,q=N.children,ae=N.isDragged;return C.createElement(he,Y({onMouseDown:te.onMouseDown,onTouchStart:te.onTouchStart,$isDragged:ae},A,_e),C.createElement(ge,Y({$isDragged:ae,ref:te.ref},A,pe),q))},renderThumb:function(N){var te=N.props,q=N.index,ae=N.isDragged,je=m||(!!q&&o||!q&&T||ae)&&!h;return C.createElement(et,Y({},te,{onMouseEnter:function(){q===0?D(!0):u(!0)},onMouseLeave:function(){q===0?D(!1):u(!1)},$thumbIndex:q,$isDragged:ae,style:Bt({},te.style)},A,tt,{$isFocusVisible:p&&x===q}),je&&C.createElement(ot,Y({$thumbIndex:q,$isDragged:ae},A,st),E(B[q])),je&&C.createElement(nt,Y({$thumbIndex:q,$isDragged:ae},A,at)))}},c?{renderMark:function(N){var te=N.props,q=N.index;return C.createElement(ht,Y({$markIndex:q},te,A,gt))}}:{})),C.createElement(lt,Y({},A,dt),C.createElement(Be,Y({},A,Ae),E(b)),C.createElement(Be,Y({},A,Ae),E(t))))}const Ut=me("div",{target:"e8lt0n70"})(({disabled:e,theme:r})=>({alignItems:"center",backgroundColor:e?r.colors.gray:r.colors.primary,borderTopLeftRadius:"100%",borderTopRightRadius:"100%",borderBottomLeftRadius:"100%",borderBottomRightRadius:"100%",borderTopStyle:"none",borderBottomStyle:"none",borderRightStyle:"none",borderLeftStyle:"none",boxShadow:"none",display:"flex",justifyContent:"center",height:r.sizes.sliderThumb,width:r.sizes.sliderThumb,":focus":{outline:"none"},":focus-visible":{boxShadow:`0 0 0 0.2rem ${Tt(r.colors.primary,.5)}`}})),Ht=me("div",{target:"e8lt0n71"})(({disabled:e,theme:r})=>({fontFamily:r.genericFonts.codeFont,fontSize:r.fontSizes.sm,color:e?r.colors.gray:r.colors.primary,top:"-1.6em",position:"absolute",whiteSpace:"nowrap",backgroundColor:r.colors.transparent,lineHeight:r.lineHeights.base,fontWeight:r.fontWeights.normal,pointerEvents:"none"})),Wt=me("div",{target:"e8lt0n72"})(({theme:e})=>({fontSize:e.fontSizes.sm,paddingBottom:e.spacing.none,paddingLeft:e.spacing.none,paddingRight:e.spacing.none,paddingTop:"0.65em",justifyContent:"space-between",alignItems:"center",display:"flex"})),Xe=me("div",{target:"e8lt0n73"})(({disabled:e,theme:r})=>({lineHeight:r.lineHeights.base,fontWeight:r.fontWeights.normal,fontFamily:r.genericFonts.codeFont,color:e?r.colors.fadedText40:"inherit"})),qt=200;function Kt({disabled:e,element:r,widgetMgr:a,fragmentId:s}){const[h,S]=xt({getStateFromWidgetMgr:Yt,getDefaultStateFromProto:Xt,getCurrStateFromProto:Zt,updateWidgetMgrState:Gt,element:r,widgetMgr:a,fragmentId:s}),[c,f]=C.useState(h),j=C.useRef(null),[F]=C.useState([]),[L]=C.useState([]),{colors:$,fonts:b,fontSizes:d,spacing:t}=Ot(),n=c.map(I=>Ee(I,r)),l=Ee(r.min,r),g=Ee(r.max,r),m=r.label;C.useEffect(()=>{f(h)},[h]);const y=C.useCallback(wt(qt,I=>{S({value:I,fromUi:!0})}),[]),E=C.useCallback(({value:I})=>{f(I),y(I)},[y]),w=C.useCallback(()=>Ve(Wt,{"data-testid":"stSliderTickBar",children:[re(Xe,{disabled:e,"data-testid":"stSliderTickBarMin",children:l}),re(Xe,{disabled:e,"data-testid":"stSliderTickBarMax",children:g})]}),[l,g,e]),R=C.useCallback(C.forwardRef(function(T,D){const{$thumbIndex:V}=T,i=V||0;F[i]=D,L[i]||=C.createRef();const o=Rt(T,["role","style","aria-valuemax","aria-valuemin","aria-valuenow","tabIndex","onKeyUp","onKeyDown","onMouseEnter","onMouseLeave","draggable"]),u=n[i];return re(Ut,{...o,disabled:T.$disabled===!0,ref:F[i],"aria-valuetext":u,"aria-label":m,children:re(Ht,{"data-testid":"stSliderThumbValue",disabled:T.$disabled===!0,ref:L[i],children:u})})}),[]);C.useEffect(()=>{L.map((o,u)=>{o.current&&(o.current.innerText=n[u])}),F.map((o,u)=>{o.current&&o.current.setAttribute("aria-valuetext",n[u])});const I=j.current??null,T=F[0].current,D=F[1]?.current,V=L[0].current,i=L[1]?.current;er(I,T,D,V,i)});const M=C.useCallback(({$disabled:I})=>({height:t.twoXS,...I?{background:$.darkenedBgMix25}:{}}),[$,t]);return Ve("div",{ref:j,className:"stSlider","data-testid":"stSlider",children:[re(Et,{label:r.label,disabled:e,labelVisibility:St(r.labelVisibility?.value),children:r.help&&re(Mt,{children:re(_t,{content:r.help,placement:$t.TOP_RIGHT})})}),re(Nt,{min:r.min,max:r.max,step:r.step,value:Qt(c,r),onChange:E,disabled:e,overrides:{Thumb:R,Tick:{style:{fontFamily:b.monospace}},Track:{style:{backgroundColor:"none !important",paddingBottom:t.none,paddingLeft:t.none,paddingRight:t.none,paddingTop:`calc(${d.sm} * 1.35)`}},InnerTrack:{style:M},TickBar:w}})]})}function Yt(e,r){return e.getDoubleArrayValue(r)}function Xt(e){return e.default}function Zt(e){return e.value}function Gt(e,r,a,s){r.setDoubleArrayValue(e,a.value,{fromUi:a.fromUi},s)}function Jt(e){const{dataType:r}=e;return r===$e.DataType.DATETIME||r===$e.DataType.DATE||r===$e.DataType.TIME}function Ee(e,r){const{format:a,options:s}=r;return Jt(r)?It.utc(e/1e3).format(a):s.length>0?ze.sprintf(a,s[e]):ze.sprintf(a,e)}function Qt(e,r){const{min:a,max:s}=r;let h=e[0],S=e.length>1?e[1]:e[0];return h>S&&(h=S),h<a&&(h=a),h>s&&(h=s),S<a&&(S=a),S>s&&(S=s),e.length>1?[h,S]:[h]}function er(e,r,a,s,h){!e||!r||!s||(ie(e,r,s),a&&h&&(ie(e,a,h),tr(e,r,a,s,h)))}function ie(e,r,a){const s=e.getBoundingClientRect(),h=r.getBoundingClientRect(),S=a.getBoundingClientRect(),c=h.left+h.width/2,f=c-S.width/2<s.left,j=c+S.width/2>s.right;a.style.left=f?"0":"",a.style.right=j?"0":""}function tr(e,r,a,s,h){const c=e.getBoundingClientRect(),f=r.getBoundingClientRect(),j=a.getBoundingClientRect(),F=s.getBoundingClientRect(),L=h.getBoundingClientRect(),$=c.left+c.width/2,b=f.left+f.width/2,d=j.left+j.width/2,t=b-F.width/2>=c.left,n=d+L.width/2<=c.right,l=f.left-F.width>=c.left,g=j.right+L.width<=c.right,m=t?F.width/2:F.width,y=n?L.width/2:L.width,E=b+m;if(d-y-E>24){ie(e,r,s),ie(e,a,h);return}if(l&&g){s.style.left="",s.style.right=`${Math.round(f.width)}px`,h.style.left=`${Math.round(j.width)}px`,h.style.right="";return}b<$?(ie(e,r,s),h.style.left=`${Math.round(b+m+24-d)}px`,h.style.right=""):(ie(e,a,h),s.style.left="",s.style.right=`${-Math.round(d-y-24-b)}px`)}const or=kt(C.memo(Kt));export{or as default};

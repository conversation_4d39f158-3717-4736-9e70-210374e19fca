const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./downloader.DKv2kGmf.js","./index.BTGIlECR.js","../css/index.CJVRHjQZ.css","./sandbox.6mITyM_H.js","./memory.CAr59PeX.js"])))=>i.map(i=>d[i]);
import{y as d}from"./index.BTGIlECR.js";const I=globalThis.showDirectoryPicker;async function j(i={}){if(I&&!i._preferPolyfill)return I(i);const e=document.createElement("input");e.type="file",e.webkitdirectory=!0,e.multiple=!0,e.style.position="fixed",e.style.top="-100000px",e.style.left="-100000px",document.body.appendChild(e);const t=d(()=>Promise.resolve().then(()=>R),void 0,import.meta.url);return await new Promise(a=>{e.addEventListener("change",a),e.click()}),t.then(a=>a.getDirHandlesFromInput(e))}const M={accepts:[]},x=globalThis.showOpenFilePicker;async function N(i={}){const e={...M,...i};if(x&&!i._preferPolyfill)return x(e);const t=document.createElement("input");t.type="file",t.multiple=e.multiple,t.accept=(e.accepts||[]).map(o=>[...(o.extensions||[]).map(n=>"."+n),...o.mimeTypes||[]]).flat().join(","),Object.assign(t.style,{position:"fixed",top:"-100000px",left:"-100000px"}),document.body.appendChild(t);const a=d(()=>Promise.resolve().then(()=>R),void 0,import.meta.url);return await new Promise(o=>{t.addEventListener("change",o,{once:!0}),t.click()}),t.remove(),a.then(o=>o.getFileHandlesFromInput(t))}const L=globalThis.showSaveFilePicker;async function V(i={}){if(L&&!i._preferPolyfill)return L(i);i._name&&(console.warn("deprecated _name, spec now have `suggestedName`"),i.suggestedName=i._name);const{FileSystemFileHandle:e}=await d(async()=>{const{FileSystemFileHandle:a}=await Promise.resolve().then(()=>g);return{FileSystemFileHandle:a}},void 0,import.meta.url),{FileHandle:t}=await d(async()=>{const{FileHandle:a}=await import("./downloader.DKv2kGmf.js");return{FileHandle:a}},__vite__mapDeps([0,1,2]),import.meta.url);return new e(new t(i.suggestedName))}globalThis.DataTransferItem&&!DataTransferItem.prototype.getAsFileSystemHandle&&(DataTransferItem.prototype.getAsFileSystemHandle=async function(){const i=this.webkitGetAsEntry(),[{FileHandle:e,FolderHandle:t},{FileSystemDirectoryHandle:a},{FileSystemFileHandle:o}]=await Promise.all([d(()=>import("./sandbox.6mITyM_H.js"),__vite__mapDeps([3,1,2]),import.meta.url),d(()=>Promise.resolve().then(()=>H),void 0,import.meta.url),d(()=>Promise.resolve().then(()=>g),void 0,import.meta.url)]);return i.isFile?new o(new e(i,!1)):new a(new t(i,!1))});async function W(i,e={}){if(!i)return globalThis.navigator?.storage?.getDirectory()||globalThis.getOriginPrivateDirectory();const{FileSystemDirectoryHandle:t}=await d(async()=>{const{FileSystemDirectoryHandle:n}=await Promise.resolve().then(()=>H);return{FileSystemDirectoryHandle:n}},void 0,import.meta.url),a=await i,o=await(a.default?a.default(e):a(e));return new t(o)}const q={WritableStream:globalThis.WritableStream,TransformStream:globalThis.TransformStream,DOMException:globalThis.DOMException,Blob:globalThis.Blob,File:globalThis.File},{WritableStream:$}=q;class p extends ${#e;constructor(e){super(e),this.#e=e,Object.setPrototypeOf(this,p.prototype),this._closed=!1}async close(){this._closed=!0;const e=this.getWriter(),t=e.close();return e.releaseLock(),t}seek(e){return this.write({type:"seek",position:e})}truncate(e){return this.write({type:"truncate",size:e})}write(e){if(this._closed)return Promise.reject(new TypeError("Cannot write to a CLOSED writable stream"));const t=this.getWriter(),a=t.write(e);return t.releaseLock(),a}}Object.defineProperty(p.prototype,Symbol.toStringTag,{value:"FileSystemWritableFileStream",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(p.prototype,{close:{enumerable:!0},seek:{enumerable:!0},truncate:{enumerable:!0},write:{enumerable:!0}});globalThis.FileSystemFileHandle&&!globalThis.FileSystemFileHandle.prototype.createWritable&&!globalThis.FileSystemWritableFileStream&&(globalThis.FileSystemWritableFileStream=p);const m=Symbol("adapter");class T{[m];name;kind;constructor(e){this.kind=e.kind,this.name=e.name,this[m]=e}async queryPermission(e={}){const{mode:t="read"}=e,a=this[m];if(a.queryPermission)return a.queryPermission({mode:t});if(t==="read")return"granted";if(t==="readwrite")return a.writable?"granted":"denied";throw new TypeError(`Mode ${t} must be 'read' or 'readwrite'`)}async requestPermission({mode:e="read"}={}){const t=this[m];if(t.requestPermission)return t.requestPermission({mode:e});if(e==="read")return"granted";if(e==="readwrite")return t.writable?"granted":"denied";throw new TypeError(`Mode ${e} must be 'read' or 'readwrite'`)}async remove(e={}){await this[m].remove(e)}async isSameEntry(e){return this===e?!0:!e||typeof e!="object"||this.kind!==e.kind||!e[m]?!1:this[m].isSameEntry(e[m])}}Object.defineProperty(T.prototype,Symbol.toStringTag,{value:"FileSystemHandle",writable:!1,enumerable:!1,configurable:!0});globalThis.FileSystemHandle&&(globalThis.FileSystemHandle.prototype.queryPermission??=function(i){return"granted"});const A={INVALID:["seeking position failed.","InvalidStateError"],GONE:["A requested file or directory could not be found at the time an operation was processed.","NotFoundError"],MISMATCH:["The path supplied exists, but was not an entry of requested type.","TypeMismatchError"],MOD_ERR:["The object can not be modified in this way.","InvalidModificationError"],SYNTAX:i=>[`Failed to execute 'write' on 'UnderlyingSinkBase': Invalid params passed. ${i}`,"SyntaxError"],SECURITY:["It was determined that certain files are unsafe for access within a Web application, or that too many calls are being made on file resources.","SecurityError"],DISALLOWED:["The request is not allowed by the user agent or the platform in the current context.","NotAllowedError"]},z={writable:globalThis.WritableStream};async function U(i){console.warn("deprecated fromDataTransfer - use `dt.items[0].getAsFileSystemHandle()` instead");const[e,t,a]=await Promise.all([d(()=>import("./memory.CAr59PeX.js"),__vite__mapDeps([4,1,2]),import.meta.url),d(()=>import("./sandbox.6mITyM_H.js"),__vite__mapDeps([3,1,2]),import.meta.url),d(()=>Promise.resolve().then(()=>H),void 0,import.meta.url)]),o=new e.FolderHandle("",!1);return o._entries=i.map(n=>n.isFile?new t.FileHandle(n,!1):new t.FolderHandle(n,!1)),new a.FileSystemDirectoryHandle(o)}async function k(i){const{FolderHandle:e,FileHandle:t}=await d(async()=>{const{FolderHandle:y,FileHandle:s}=await import("./memory.CAr59PeX.js");return{FolderHandle:y,FileHandle:s}},__vite__mapDeps([4,1,2]),import.meta.url),{FileSystemDirectoryHandle:a}=await d(async()=>{const{FileSystemDirectoryHandle:y}=await Promise.resolve().then(()=>H);return{FileSystemDirectoryHandle:y}},void 0,import.meta.url),o=Array.from(i.files),n=o[0].webkitRelativePath.split("/",1)[0],l=new e(n,!1);return o.forEach(y=>{const s=y.webkitRelativePath.split("/");s.shift();const c=s.pop(),f=s.reduce((w,b)=>(w._entries[b]||(w._entries[b]=new e(b,!1)),w._entries[b]),l);f._entries[c]=new t(y.name,y,!1)}),new a(l)}async function C(i){const{FileHandle:e}=await d(async()=>{const{FileHandle:a}=await import("./memory.CAr59PeX.js");return{FileHandle:a}},__vite__mapDeps([4,1,2]),import.meta.url),{FileSystemFileHandle:t}=await d(async()=>{const{FileSystemFileHandle:a}=await Promise.resolve().then(()=>g);return{FileSystemFileHandle:a}},void 0,import.meta.url);return Array.from(i.files).map(a=>new t(new e(a.name,a,!1)))}const R=Object.freeze(Object.defineProperty({__proto__:null,config:z,errors:A,fromDataTransfer:U,getDirHandlesFromInput:k,getFileHandlesFromInput:C},Symbol.toStringTag,{value:"Module"})),{GONE:B,MOD_ERR:G}=A,u=Symbol("adapter");let h=class v extends T{[u];constructor(e){super(e),this[u]=e}async getDirectoryHandle(e,t={}){if(e==="")throw new TypeError("Name can't be an empty string.");if(e==="."||e===".."||e.includes("/"))throw new TypeError("Name contains invalid characters.");t.create=!!t.create;const a=await this[u].getDirectoryHandle(e,t);return new v(a)}async*entries(){const{FileSystemFileHandle:e}=await d(async()=>{const{FileSystemFileHandle:t}=await Promise.resolve().then(()=>g);return{FileSystemFileHandle:t}},void 0,import.meta.url);for await(const[t,a]of this[u].entries())yield[a.name,a.kind==="file"?new e(a):new v(a)]}async*getEntries(){const{FileSystemFileHandle:e}=await d(async()=>{const{FileSystemFileHandle:t}=await Promise.resolve().then(()=>g);return{FileSystemFileHandle:t}},void 0,import.meta.url);console.warn("deprecated, use .entries() instead");for await(let t of this[u].entries())yield t.kind==="file"?new e(t):new v(t)}async getFileHandle(e,t={}){const{FileSystemFileHandle:a}=await d(async()=>{const{FileSystemFileHandle:n}=await Promise.resolve().then(()=>g);return{FileSystemFileHandle:n}},void 0,import.meta.url);if(e==="")throw new TypeError("Name can't be an empty string.");if(e==="."||e===".."||e.includes("/"))throw new TypeError("Name contains invalid characters.");t.create=!!t.create;const o=await this[u].getFileHandle(e,t);return new a(o)}async removeEntry(e,t={}){if(e==="")throw new TypeError("Name can't be an empty string.");if(e==="."||e===".."||e.includes("/"))throw new TypeError("Name contains invalid characters.");return t.recursive=!!t.recursive,this[u].removeEntry(e,t)}async resolve(e){if(await e.isSameEntry(this))return[];const t=[{handle:this,path:[]}];for(;t.length;){let{handle:a,path:o}=t.pop();for await(const n of a.values()){if(await n.isSameEntry(e))return[...o,n.name];n.kind==="directory"&&t.push({handle:n,path:[...o,n.name]})}}return null}async*keys(){for await(const[e]of this[u].entries())yield e}async*values(){for await(const[e,t]of this)yield t}[Symbol.asyncIterator](){return this.entries()}};Object.defineProperty(h.prototype,Symbol.toStringTag,{value:"FileSystemDirectoryHandle",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(h.prototype,{getDirectoryHandle:{enumerable:!0},entries:{enumerable:!0},getFileHandle:{enumerable:!0},removeEntry:{enumerable:!0}});if(globalThis.FileSystemDirectoryHandle){const i=globalThis.FileSystemDirectoryHandle.prototype;i.resolve=async function(n){if(await n.isSameEntry(this))return[];const l=[{handle:this,path:[]}];for(;l.length;){let{handle:y,path:s}=l.pop();for await(const c of y.values()){if(await c.isSameEntry(n))return[...s,c.name];c.kind==="directory"&&l.push({handle:c,path:[...s,c.name]})}}return null};async function e(o){if(await(await navigator.storage.getDirectory()).resolve(o)===null)throw new DOMException(...B)}const t=i.entries;i.entries=async function*(){await e(this),yield*t.call(this)},i[Symbol.asyncIterator]=async function*(){yield*this.entries()};const a=i.removeEntry;i.removeEntry=async function(o,n={}){return a.call(this,o,n).catch(async l=>{throw l instanceof DOMException&&l.name==="UnknownError"&&!n.recursive&&!(await t.call(this).next()).done?new DOMException(...G):l})}}const H=Object.freeze(Object.defineProperty({__proto__:null,FileSystemDirectoryHandle:h,default:h},Symbol.toStringTag,{value:"Module"})),{INVALID:Y,SYNTAX:O,GONE:X}=A,E=Symbol("adapter");class F extends T{[E];constructor(e){super(e),this[E]=e}async createWritable(e={}){return new p(await this[E].createWritable(e))}async getFile(){return this[E].getFile()}}Object.defineProperty(F.prototype,Symbol.toStringTag,{value:"FileSystemFileHandle",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(F.prototype,{createWritable:{enumerable:!0},getFile:{enumerable:!0}});if(globalThis.FileSystemFileHandle&&!globalThis.FileSystemFileHandle.prototype.createWritable){const i=new WeakMap;let e;const t=()=>{let o,n;onmessage=async l=>{const y=l.ports[0],s=l.data;switch(s.type){case"open":const c=s.name;let f=await navigator.storage.getDirectory();for(const w of s.path)f=await f.getDirectoryHandle(w);o=await f.getFileHandle(c),n=await o.createSyncAccessHandle();break;case"write":n.write(s.data,{at:s.position}),n.flush();break;case"truncate":n.truncate(s.size);break;case"abort":case"close":n.close();break}y.postMessage(0)}};globalThis.FileSystemFileHandle.prototype.createWritable=async function(o){if(!e){const r=`(${t.toString()})()`,S=new Blob([r],{type:"text/javascript"});e=URL.createObjectURL(S)}const n=new Worker(e,{type:"module"});let l=0;const y=new TextEncoder;let s=await this.getFile().then(r=>r.size);const c=r=>new Promise((S,P)=>{const _=new MessageChannel;_.port1.onmessage=D=>{D.data instanceof Error?P(D.data):S(D.data),_.port1.close(),_.port2.close(),_.port1.onmessage=null},n.postMessage(r,[_.port2])}),f=await navigator.storage.getDirectory(),w=await i.get(this),b=await f.resolve(w);if(b===null)throw new DOMException(...X);return await c({type:"open",path:b,name:this.name}),o?.keepExistingData===!1&&(await c({type:"truncate",size:0}),s=0),new p({start:r=>{},async write(r){if(r?.constructor===Object?r={...r}:r={type:"write",data:r,position:l},r.type==="write"){if(!("data"in r))throw await c({type:"close"}),new DOMException(...O("write requires a data argument"));if(r.position??=l,typeof r.data=="string")r.data=y.encode(r.data);else if(r.data instanceof ArrayBuffer)r.data=new Uint8Array(r.data);else if(!(r.data instanceof Uint8Array)&&ArrayBuffer.isView(r.data))r.data=new Uint8Array(r.data.buffer,r.data.byteOffset,r.data.byteLength);else if(!(r.data instanceof Uint8Array)){const P=await new Response(r.data).arrayBuffer();r.data=new Uint8Array(P)}Number.isInteger(r.position)&&r.position>=0&&(l=r.position),l+=r.data.byteLength,s+=r.data.byteLength}else if(r.type==="seek")if(Number.isInteger(r.position)&&r.position>=0){if(s<r.position)throw new DOMException(...Y);console.log("seeking",r),l=r.position;return}else throw await c({type:"close"}),new DOMException(...O("seek requires a position argument"));else if(r.type==="truncate")if(Number.isInteger(r.size)&&r.size>=0)s=r.size,l>s&&(l=s);else throw await c({type:"close"}),new DOMException(...O("truncate requires a size argument"));await c(r)},async close(){await c({type:"close"}),n.terminate()},async abort(r){await c({type:"abort",reason:r}),n.terminate()}})};const a=FileSystemDirectoryHandle.prototype.getFileHandle;FileSystemDirectoryHandle.prototype.getFileHandle=async function(...o){const n=await a.call(this,...o);return i.set(n,this),n}}const g=Object.freeze(Object.defineProperty({__proto__:null,FileSystemFileHandle:F,default:F},Symbol.toStringTag,{value:"Module"})),Q=Object.freeze(Object.defineProperty({__proto__:null,FileSystemDirectoryHandle:h,FileSystemFileHandle:F,FileSystemHandle:T,FileSystemWritableFileStream:p,getOriginPrivateDirectory:W,showDirectoryPicker:j,showOpenFilePicker:N,showSaveFilePicker:V},Symbol.toStringTag,{value:"Module"}));export{Q as a,q as c,A as e};

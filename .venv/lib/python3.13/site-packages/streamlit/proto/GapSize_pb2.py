# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/GapSize.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dstreamlit/proto/GapSize.proto\x12\tstreamlit\"?\n\tGapConfig\x12&\n\x08gap_size\x18\x01 \x01(\x0e\x32\x12.streamlit.GapSizeH\x00\x42\n\n\x08gap_spec*H\n\x07GapSize\x12\x11\n\rGAP_UNDEFINED\x10\x00\x12\t\n\x05SMALL\x10\x01\x12\n\n\x06MEDIUM\x10\x02\x12\t\n\x05LARGE\x10\x03\x12\x08\n\x04NONE\x10\x04\x42,\n\x1c\x63om.snowflake.apps.streamlitB\x0cGapSizeProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.GapSize_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\014GapSizeProto'
  _globals['_GAPSIZE']._serialized_start=109
  _globals['_GAPSIZE']._serialized_end=181
  _globals['_GAPCONFIG']._serialized_start=44
  _globals['_GAPCONFIG']._serialized_end=107
# @@protoc_insertion_point(module_scope)
